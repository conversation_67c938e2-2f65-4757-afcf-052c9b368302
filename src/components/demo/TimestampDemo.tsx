'use client';

import React from 'react';
import styled from 'styled-components';
import { formatSmartTimestamp } from '@/utils/dateUtils';
import { appTheme } from '@/app/theme';

const DemoContainer = styled.div`
  padding: ${appTheme.spacing.xl};
  background: ${appTheme.colors.background.light};
  border-radius: ${appTheme.borderRadius.lg};
  margin: ${appTheme.spacing.lg};
`;

const DemoTitle = styled.h2`
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.lg};
  font-size: 24px;
  font-weight: 600;
`;

const ExampleList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.md};
`;

const ExampleItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.md};
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.md};
  border: 1px solid ${appTheme.colors.border};
`;

const ExampleLabel = styled.span`
  color: ${appTheme.colors.text.secondary};
  font-weight: 500;
`;

const ExampleResult = styled.span`
  color: ${appTheme.colors.text.primary};
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: ${appTheme.colors.background.lighter};
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.sm};
`;

const TimestampDemo: React.FC = () => {
  const now = new Date();
  
  // Create example timestamps
  const examples = [
    {
      label: 'Right now',
      timestamp: now.toISOString(),
    },
    {
      label: '2 hours ago (today)',
      timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(),
    },
    {
      label: 'Yesterday',
      timestamp: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      label: '3 days ago (this week)',
      timestamp: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      label: '10 days ago (this month)',
      timestamp: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      label: '2 months ago (this year)',
      timestamp: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      label: 'Last year',
      timestamp: new Date(now.getFullYear() - 1, 5, 15, 14, 30).toISOString(),
    },
  ];

  return (
    <DemoContainer>
      <DemoTitle>Smart Timestamp Formatting Demo</DemoTitle>
      <ExampleList>
        {examples.map((example, index) => (
          <ExampleItem key={index}>
            <ExampleLabel>{example.label}:</ExampleLabel>
            <ExampleResult>{formatSmartTimestamp(example.timestamp)}</ExampleResult>
          </ExampleItem>
        ))}
      </ExampleList>
    </DemoContainer>
  );
};

export default TimestampDemo;
