/**
 * Test file for dateUtils functions
 * This demonstrates the smart timestamp formatting functionality
 */

import { formatSmartTimestamp, formatSimpleTimestamp, formatReadTime, formatFullDateTime } from '../dateUtils';

// Mock current date for consistent testing
const mockNow = new Date('2024-01-15T14:30:00Z'); // Monday, January 15, 2024, 2:30 PM UTC

// Helper function to create test dates relative to mockNow
const createTestDate = (daysAgo: number, hoursAgo: number = 0, minutesAgo: number = 0): string => {
  const date = new Date(mockNow);
  date.setDate(date.getDate() - daysAgo);
  date.setHours(date.getHours() - hoursAgo);
  date.setMinutes(date.getMinutes() - minutesAgo);
  return date.toISOString();
};

describe('Smart Timestamp Formatting', () => {
  beforeAll(() => {
    // Mock Date.now() to return our fixed date
    jest.spyOn(Date, 'now').mockImplementation(() => mockNow.getTime());
    // Mock new Date() constructor when called without arguments
    const OriginalDate = Date;
    global.Date = class extends OriginalDate {
      constructor(...args: any[]) {
        if (args.length === 0) {
          super(mockNow.getTime());
        } else {
          super(...args);
        }
      }
    } as any;
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('formatSmartTimestamp', () => {
    test('shows time only for messages from today', () => {
      const todayMessage = createTestDate(0, 2, 15); // 2 hours 15 minutes ago
      const result = formatSmartTimestamp(todayMessage);
      expect(result).toMatch(/^\d{2}:\d{2}$/); // Should be in HH:MM format
    });

    test('shows day and time for messages from this week', () => {
      const yesterdayMessage = createTestDate(1, 5); // Yesterday, 5 hours ago
      const result = formatSmartTimestamp(yesterdayMessage);
      expect(result).toMatch(/^(Sun|Mon|Tue|Wed|Thu|Fri|Sat), \d{2}:\d{2}$/);
    });

    test('shows month, day and time for messages from this month', () => {
      const thisMonthMessage = createTestDate(10); // 10 days ago
      const result = formatSmartTimestamp(thisMonthMessage);
      expect(result).toMatch(/^Jan \d{1,2}, \d{2}:\d{2}$/);
    });

    test('shows month, day and time for messages from this year', () => {
      const thisYearMessage = createTestDate(60); // 60 days ago (different month)
      const result = formatSmartTimestamp(thisYearMessage);
      expect(result).toMatch(/^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{1,2}, \d{2}:\d{2}$/);
    });

    test('shows full date with year for messages from previous years', () => {
      const lastYearMessage = new Date('2023-06-15T10:30:00Z').toISOString();
      const result = formatSmartTimestamp(lastYearMessage);
      expect(result).toMatch(/^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{1,2}, 2023, \d{2}:\d{2}$/);
    });
  });

  describe('formatSimpleTimestamp', () => {
    test('shows time for recent messages', () => {
      const recentMessage = createTestDate(0, 2);
      const result = formatSimpleTimestamp(recentMessage);
      expect(result).toMatch(/^\d{2}:\d{2}$/);
    });

    test('shows date for older messages', () => {
      const oldMessage = createTestDate(2);
      const result = formatSimpleTimestamp(oldMessage);
      expect(result).toMatch(/^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{1,2}$/);
    });

    test('returns empty string for undefined input', () => {
      const result = formatSimpleTimestamp(undefined);
      expect(result).toBe('');
    });
  });

  describe('formatReadTime', () => {
    test('shows "Just now" for very recent reads', () => {
      const justNow = new Date(mockNow.getTime() - 30000).toISOString(); // 30 seconds ago
      const result = formatReadTime(justNow);
      expect(result).toBe('Just now');
    });

    test('shows minutes for recent reads', () => {
      const fiveMinutesAgo = new Date(mockNow.getTime() - 5 * 60 * 1000).toISOString();
      const result = formatReadTime(fiveMinutesAgo);
      expect(result).toBe('5m ago');
    });

    test('shows hours for reads within a day', () => {
      const twoHoursAgo = new Date(mockNow.getTime() - 2 * 60 * 60 * 1000).toISOString();
      const result = formatReadTime(twoHoursAgo);
      expect(result).toBe('2h ago');
    });

    test('shows date for older reads', () => {
      const twoDaysAgo = createTestDate(2);
      const result = formatReadTime(twoDaysAgo);
      expect(result).toMatch(/^\d{1,2}\/\d{1,2}\/\d{4}$/);
    });
  });

  describe('formatFullDateTime', () => {
    test('formats full date and time', () => {
      const testDate = '2024-01-15T14:30:00Z';
      const result = formatFullDateTime(testDate);
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });

    test('returns original string for invalid dates', () => {
      const invalidDate = 'invalid-date';
      const result = formatFullDateTime(invalidDate);
      expect(result).toBe(invalidDate);
    });
  });
});

// Example usage demonstration
console.log('=== Smart Timestamp Examples ===');
console.log('Current time:', mockNow.toISOString());
console.log('Today (2h ago):', formatSmartTimestamp(createTestDate(0, 2)));
console.log('Yesterday:', formatSmartTimestamp(createTestDate(1)));
console.log('This week (3 days ago):', formatSmartTimestamp(createTestDate(3)));
console.log('This month (10 days ago):', formatSmartTimestamp(createTestDate(10)));
console.log('This year (60 days ago):', formatSmartTimestamp(createTestDate(60)));
console.log('Last year:', formatSmartTimestamp('2023-06-15T10:30:00Z'));
