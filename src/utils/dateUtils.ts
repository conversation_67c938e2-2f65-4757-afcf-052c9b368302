/**
 * Date utility functions for consistent timestamp formatting across the application
 */

/**
 * Smart timestamp formatting that shows contextually appropriate date/time information
 * based on how recent the timestamp is
 * 
 * @param timeString - ISO timestamp string
 * @returns Formatted timestamp string
 */
export const formatSmartTimestamp = (timeString: string): string => {
  const date = new Date(timeString);
  const now = new Date();
  
  // Get start of today, this week, this month, and this year
  const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
  startOfWeek.setHours(0, 0, 0, 0);
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const startOfYear = new Date(now.getFullYear(), 0, 1);
  
  // Time formatting options
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  };
  
  // Check if message is from today
  if (date >= startOfToday) {
    return date.toLocaleTimeString('en-US', timeOptions);
  }
  
  // Check if message is from this week
  if (date >= startOfWeek) {
    const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
    const time = date.toLocaleTimeString('en-US', timeOptions);
    return `${dayName}, ${time}`;
  }
  
  // Check if message is from this month
  if (date >= startOfMonth) {
    const monthDay = date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
    const time = date.toLocaleTimeString('en-US', timeOptions);
    return `${monthDay}, ${time}`;
  }
  
  // Check if message is from this year
  if (date >= startOfYear) {
    const monthDay = date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
    const time = date.toLocaleTimeString('en-US', timeOptions);
    return `${monthDay}, ${time}`;
  }
  
  // Message is from previous years
  const fullDate = date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: 'numeric'
  });
  const time = date.toLocaleTimeString('en-US', timeOptions);
  return `${fullDate}, ${time}`;
};

/**
 * Simple timestamp formatting for sidebar/list views
 * Shows time for today, date for older messages
 * 
 * @param timeString - ISO timestamp string
 * @returns Formatted timestamp string
 */
export const formatSimpleTimestamp = (timeString?: string): string => {
  if (!timeString) return '';

  const date = new Date(timeString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));

  if (hours < 24) {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  }
};

/**
 * Format timestamp for read status tooltips
 * Shows relative time (e.g., "5m ago", "2h ago", "Just now")
 * 
 * @param readAt - ISO timestamp string
 * @returns Formatted relative time string
 */
export const formatReadTime = (readAt: string): string => {
  const date = new Date(readAt);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return date.toLocaleDateString();
};

/**
 * Format full date and time for detailed views
 * 
 * @param timestamp - ISO timestamp string
 * @returns Formatted full date and time string
 */
export const formatFullDateTime = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleString();
  } catch {
    return timestamp;
  }
};
